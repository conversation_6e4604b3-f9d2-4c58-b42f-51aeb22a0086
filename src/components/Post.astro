---
import { formatDate, getEssayDisplayTitle } from '~/utils'
import type { CollectionEntry } from 'astro:content'

interface Props {
  post: CollectionEntry<'posts'> | CollectionEntry<'essays'>
}

const { post } = Astro.props
const { translate: t } = Astro.locals

// 根据集合类型确定链接前缀和分类链接前缀
const isEssay = post.collection === 'essays'
const linkPrefix = isEssay ? '/essays/' : '/posts/'
const categoryPrefix = isEssay ? '/essay-categories/' : '/categories/'

// 获取显示标题
const displayTitle = isEssay ? getEssayDisplayTitle(post as CollectionEntry<'essays'>) : post.data.title

// 获取分类（只有posts有分类）
const categories = !isEssay && post.collection === 'posts' ? (post as CollectionEntry<'posts'>).data.categories : []
---

<article class="heti">
  <header>
    {isEssay ? (
      // 随笔样式：更简洁，类似微博
      <div class="mb-4">
        <div class="text-3.5 text-gray-500 mb-2">
          <time>{formatDate(post.data.pubDate)}</time>
        </div>
      </div>
    ) : (
      // 博客文章样式：保持原有样式
      <>
        <h1 class="post-title!">
          <a href={`${linkPrefix}${post.slug}/`}>{displayTitle}</a>
        </h1>
        <div class="mt-2 text-3.5">
          <span>{t('posted_at')}</span>
          <time>{formatDate(post.data.pubDate)}</time>
          {
            categories.map((category: string) => (
              <a class="ml-2.5" href={`${categoryPrefix}${category}`}>
                # {category}
              </a>
            ))
          }
        </div>
      </>
    )}
  </header>
  <slot />
</article>
